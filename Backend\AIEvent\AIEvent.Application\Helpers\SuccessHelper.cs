using AIEvent.Application.Constants;
using AIEvent.Application.DTO.Common;
using Microsoft.AspNetCore.Mvc;
using System.Net;

namespace AIEvent.Application.Helpers
{
    public static class SuccessHelper
    {
        public static IActionResult Create<T>(T data, HttpStatusCode statusCode = HttpStatusCode.OK, string? message = null)
        {
            var response = SuccessCodeMapper.GetSuccess<T>(data, statusCode, message);
            
            return statusCode switch
            {
                HttpStatusCode.OK => new OkObjectResult(response),
                HttpStatusCode.Created => new ObjectResult(response) { StatusCode = 201 },
                HttpStatusCode.NoContent => new NoContentResult(),
                _ => new OkObjectResult(response),
            };
        }

        public static IActionResult CreateOk<T>(T data, string? message = null)
        {
            return Create(data, HttpStatusCode.OK, message);
        }

        public static IActionResult CreateCreated<T>(T data, string? message = null)
        {
            return Create(data, HttpStatusCode.Created, message);
        }

        public static IActionResult CreateUpdated<T>(T data, string? message = null)
        {
            var response = SuccessReponse<T>.SuccessResult(
                data, 
                SuccessCodes.Updated, 
                message ?? SuccessMessages.Updated);
            
            return new OkObjectResult(response);
        }

        public static IActionResult CreateDeleted(string? message = null)
        {
            var response = SuccessReponse<object>.SuccessResult(
                new { }, 
                SuccessCodes.Deleted, 
                message ?? SuccessMessages.Deleted);
            
            return new OkObjectResult(response);
        }
    }
}
