using AIEvent.Application.Constants;
using AIEvent.Application.DTO.Auth;
using AIEvent.Application.DTO.Common;
using AIEvent.Application.DTO.User;
using AIEvent.Application.Helpers;
using AIEvent.Application.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;

namespace AIEvent.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;

        public AuthController(IAuthService authService)
        {
            _authService = authService;
        }

        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return ErrorHelper.Create(HttpStatusCode.BadRequest);
                }

                var result = await _authService.LoginAsync(request);

                if (result == null)
                {
                    return BadRequest(ErrorResponse.FailureResult(
                        "Invalid email or password",
                        statusCode: ErrorCodes.Unauthorized));
                }

                return Ok(SuccessReponse<AuthResponse>.SuccessResult(
                    result,
                    message: "Login successful"));
            }
            catch (Exception)
            {
                return StatusCode(500, ErrorResponse.FailureResult(
                    "An internal server error occurred",
                    statusCode: ErrorCodes.InternalServerError));
            }
        }

        /// <summary>
        /// User registration
        /// </summary>
        /// <param name="request">Registration data</param>
        /// <returns>Authentication response with tokens</returns>
        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<IActionResult> Register([FromBody] RegisterRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList();
                    return BadRequest(ErrorResponse.FailureResult(
                        "Invalid input data",
                        errors,
                        ErrorCodes.ValidationFailed));
                }

                var result = await _authService.RegisterAsync(request);

                if (result == null)
                {
                    return BadRequest(ErrorResponse.FailureResult(
                        "Registration failed. Email may already exist.",
                        statusCode: ErrorCodes.InvalidInput));
                }

                return Ok(SuccessReponse<AuthResponse>.SuccessResult(
                    result,
                    SuccessCodes.Created,
                    "Registration successful"));
            }
            catch (Exception)
            {
                return StatusCode(500, ErrorResponse.FailureResult(
                    "An internal server error occurred",
                    statusCode: ErrorCodes.InternalServerError));
            }
        }

        /// <summary>
        /// Refresh access token using refresh token
        /// </summary>
        /// <param name="request">Refresh token request</param>
        /// <returns>New authentication response with tokens</returns>
        [HttpPost("refresh-token")]
        [AllowAnonymous]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList();
                    return BadRequest(ErrorResponse.FailureResult(
                        "Invalid input data",
                        errors,
                        ErrorCodes.ValidationFailed));
                }

                var result = await _authService.RefreshTokenAsync(request.RefreshToken);

                if (result == null)
                {
                    return BadRequest(ErrorResponse.FailureResult(
                        "Invalid or expired refresh token",
                        statusCode: ErrorCodes.TokenInvalid));
                }

                return Ok(SuccessReponse<AuthResponse>.SuccessResult(
                    result,
                    message: "Token refreshed successfully"));
            }
            catch (Exception)
            {
                return StatusCode(500, ErrorResponse.FailureResult(
                    "An internal server error occurred",
                    statusCode: ErrorCodes.InternalServerError));
            }
        }

        /// <summary>
        /// Revoke refresh token (logout)
        /// </summary>
        /// <param name="request">Refresh token to revoke</param>
        /// <returns>Success response</returns>
        [HttpPost("revoke-token")]
        [Authorize]
        public async Task<IActionResult> RevokeToken([FromBody] RefreshTokenRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList();
                    return BadRequest(ErrorResponse.FailureResult(
                        "Invalid input data",
                        errors,
                        ErrorCodes.ValidationFailed));
                }

                await _authService.RevokeRefreshTokenAsync(request.RefreshToken);

                return Ok(SuccessReponse<object>.SuccessResult(
                    new { },
                    message: "Token revoked successfully"));
            }
            catch (Exception)
            {
                return StatusCode(500, ErrorResponse.FailureResult(
                    "An internal server error occurred",
                    statusCode: ErrorCodes.InternalServerError));
            }
        }

        /// <summary>
        /// Get current user information
        /// </summary>
        /// <returns>Current user info</returns>
        [HttpGet("me")]
        [Authorize]
        public Task<IActionResult> GetCurrentUser()
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Task.FromResult<IActionResult>(
                        Unauthorized(ErrorResponse.FailureResult(
                            "User not found",
                            statusCode: ErrorCodes.Unauthorized)));
                }

                var userInfo = new UserResponse
                {
                    Id = userId,
                    Email = User.FindFirst(System.Security.Claims.ClaimTypes.Email)?.Value ?? "",
                    FullName = User.FindFirst("FullName")?.Value ?? "",
                    Roles = [.. User.FindAll(System.Security.Claims.ClaimTypes.Role).Select(c => c.Value)]
                };

                return Task.FromResult<IActionResult>(
                    Ok(SuccessReponse<UserResponse>.SuccessResult(
                        userInfo,
                        message: "User information retrieved successfully")));
            }
            catch (Exception)
            {
                return Task.FromResult<IActionResult>(
                    StatusCode(500, ErrorResponse.FailureResult(
                        "An internal server error occurred",
                        statusCode: ErrorCodes.InternalServerError)));
            }
        }
    }
}
